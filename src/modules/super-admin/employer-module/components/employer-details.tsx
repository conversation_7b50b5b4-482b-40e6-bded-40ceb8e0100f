'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  Building2,
  Mail,
  Phone,
  MapPin,
  Users,
  Calendar,
  Edit,
  Plus,
  Search,
  MoreHorizontal,
  Eye,
  Trash2,
  ChevronLeft,
  ChevronRight,
  User,
  CheckCircle,
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useRouter } from 'nextjs-toploader/app';
import { CreateUpdateEmployerModal } from './create-update-employer-modal';
import { Employer } from '../api/useEmployers';
import { useQueryClient } from '@tanstack/react-query';
import Header from '@/components/reusable-component/header';

// Sample employee data for the selected employer
const employeeData = [
  {
    id: 1,
    name: 'Alice Johnson',
    email: '<EMAIL>',
    position: 'Senior Developer',
    department: 'Engineering',
    joinDate: '2022-03-15',
    status: 'active',
    salary: '$85,000',
    phone: '+****************',
  },
  {
    id: 2,
    name: 'Bob Smith',
    email: '<EMAIL>',
    position: 'Product Manager',
    department: 'Product',
    joinDate: '2021-11-20',
    status: 'active',
    salary: '$95,000',
    phone: '+****************',
  },
  {
    id: 3,
    name: 'Carol Davis',
    email: '<EMAIL>',
    position: 'UX Designer',
    department: 'Design',
    joinDate: '2023-01-10',
    status: 'active',
    salary: '$75,000',
    phone: '+****************',
  },
  {
    id: 4,
    name: 'David Wilson',
    email: '<EMAIL>',
    position: 'DevOps Engineer',
    department: 'Engineering',
    joinDate: '2022-08-05',
    status: 'inactive',
    salary: '$90,000',
    phone: '+****************',
  },
  {
    id: 5,
    name: 'Emma Brown',
    email: '<EMAIL>',
    position: 'Marketing Specialist',
    department: 'Marketing',
    joinDate: '2023-02-28',
    status: 'active',
    salary: '$65,000',
    phone: '+****************',
  },
  {
    id: 6,
    name: 'Frank Miller',
    email: '<EMAIL>',
    position: 'Sales Representative',
    department: 'Sales',
    joinDate: '2022-12-01',
    status: 'active',
    salary: '$70,000',
    phone: '+****************',
  },
  {
    id: 7,
    name: 'Grace Lee',
    email: '<EMAIL>',
    position: 'HR Manager',
    department: 'Human Resources',
    joinDate: '2021-09-15',
    status: 'active',
    salary: '$80,000',
    phone: '+****************',
  },
  {
    id: 8,
    name: 'Henry Taylor',
    email: '<EMAIL>',
    position: 'Quality Assurance',
    department: 'Engineering',
    joinDate: '2023-04-12',
    status: 'active',
    salary: '$72,000',
    phone: '+****************',
  },
  {
    id: 9,
    name: 'Ivy Chen',
    email: '<EMAIL>',
    position: 'Data Analyst',
    department: 'Analytics',
    joinDate: '2022-06-20',
    status: 'inactive',
    salary: '$78,000',
    phone: '+****************',
  },
  {
    id: 10,
    name: 'Jack Rodriguez',
    email: '<EMAIL>',
    position: 'Frontend Developer',
    department: 'Engineering',
    joinDate: '2023-03-08',
    status: 'active',
    salary: '$82,000',
    phone: '+****************',
  },
  {
    id: 11,
    name: 'Kate Anderson',
    email: '<EMAIL>',
    position: 'Backend Developer',
    department: 'Engineering',
    joinDate: '2022-01-25',
    status: 'active',
    salary: '$88,000',
    phone: '+****************',
  },
  {
    id: 12,
    name: 'Liam Thompson',
    email: '<EMAIL>',
    position: 'Project Manager',
    department: 'Operations',
    joinDate: '2021-07-10',
    status: 'active',
    salary: '$92,000',
    phone: '+****************',
  },
];

interface EmployerDetailProps {
  employer: {
    id: string;
    companyName: string;
    contactPerson: string;
    email: string;
    status: string;
    phone: string;
    address: string;
    employeeCount: number;
    createdAt: string;
    updatedAt: string;
  };
  // Add the raw employer data for the modal
  rawEmployerData?: Employer;
}

export const EmployerDetail = ({ employer, rawEmployerData }: EmployerDetailProps) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);

  const handleApproval = () => {
    setApprovalDialogOpen(true);
  };

  const confirmApproval = () => {
    // Here you would typically make an API call to approve the employer
    console.log(`Approving employer ${employer.id}`);

    toast.success(`${employer.companyName} has been approved successfully.`);

    // Close the dialog
    setApprovalDialogOpen(false);

    // In a real app, you'd update the state or refetch data and redirect
  };

  const handleEditEmployer = () => {
    setEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditModalOpen(false);
  };

  const handleEmployerUpdated = () => {
    // Refresh the page or refetch data after successful update
    queryClient.invalidateQueries({ queryKey: ['employer-details', employer.id] });
  };

  // Filter employees
  const filteredEmployees =
    employer.status === 'pending'
      ? []
      : employeeData.filter((employee) => {
          const matchesSearch =
            employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            employee.phone.toLowerCase().includes(searchTerm.toLowerCase());

          const matchesStatus = statusFilter === 'all' || employee.status === statusFilter;

          return matchesSearch && matchesStatus;
        });

  // Pagination calculations
  const totalItems = filteredEmployees.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentEmployees = filteredEmployees.slice(startIndex, endIndex);

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getEmployerStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate statistics
  const activeEmployees =
    employer.status === 'pending'
      ? 0
      : employeeData.filter((emp) => emp.status === 'active').length;
  const inactiveEmployees =
    employer.status === 'pending'
      ? 0
      : employeeData.filter((emp) => emp.status === 'inactive').length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()} className="h-10 w-10">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Header
            title={employer.companyName}
            description="Company details and employee management"
          />
        </div>
        <div className="flex items-center gap-3">
          {employer.status === 'pending' && (
            <Button onClick={handleApproval} className="h-10 bg-green-600 px-6 hover:bg-green-700">
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve Employer
            </Button>
          )}
          <Button onClick={handleEditEmployer} className="bg-primary hover:bg-primary/90 h-10 px-6">
            <Edit className="mr-2 h-4 w-4" />
            Edit Company
          </Button>
        </div>
      </div>

      {/* Company Overview Section */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Company Information - Takes 2 columns */}
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <Building2 className="text-primary h-5 w-5" />
                </div>
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Row */}
              <div className="flex items-center justify-between border-b border-gray-100 py-3">
                <span className="text-sm font-medium text-gray-600">Status</span>
                {getEmployerStatusBadge(employer.status)}
              </div>

              {/* Contact Information Grid */}
              <div className="grid gap-6 sm:grid-cols-2">
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Mail className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Email Address</p>
                    <p className="truncate text-sm text-gray-600">{employer.email}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Phone className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Phone Number</p>
                    <p className="text-sm text-gray-600">{employer.phone}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Contact Person</p>
                    <p className="text-sm text-gray-600">{employer.contactPerson}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Calendar className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Member Since</p>
                    <p className="text-sm text-gray-600">{formatDate(employer.createdAt)}</p>
                  </div>
                </div>
              </div>

              {/* Address - Full Width */}
              <div className="flex items-start gap-3 pt-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                  <MapPin className="h-4 w-4 text-gray-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900">Business Address</p>
                  <p className="text-sm leading-relaxed text-gray-600">{employer.address}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Employee Statistics - Takes 1 column */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <Users className="text-primary h-5 w-5" />
                </div>
                Employee Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Total Employees */}
              <div className="border-primary/20 from-primary/5 to-primary/10 rounded-xl border bg-gradient-to-br p-6 text-center">
                <div className="text-primary mb-1 text-3xl font-bold">12</div>
                <div className="text-sm font-medium text-gray-600">Total Employees</div>
              </div>

              {/* Active/Inactive Breakdown */}
              <div className="grid grid-cols-2 gap-4">
                <div className="rounded-lg border border-green-200 bg-green-50 p-4 text-center">
                  <div className="mb-1 text-2xl font-bold text-green-700">5</div>
                  <div className="text-xs font-medium text-green-600">Active</div>
                </div>
                <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
                  <div className="mb-1 text-2xl font-bold text-gray-700">7</div>
                  <div className="text-xs font-medium text-gray-600">Inactive</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Employee List Section */}
      {/* <Card>
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Employee Directory</CardTitle>
              <CardDescription className="text-base">
                Manage all employees under {employer.companyName}
              </CardDescription>
            </div>
            <Button
              className="bg-primary hover:bg-primary/90 h-10 px-6"
              disabled={employer.status === 'pending'}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Employee
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>
            <div className="flex gap-3">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value);
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="h-10 w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="h-10 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="overflow-hidden rounded-lg border border-gray-200">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-gray-900">Employee</TableHead>
                  <TableHead className="font-semibold text-gray-900">Phone Number</TableHead>
                  <TableHead className="font-semibold text-gray-900">Join Date</TableHead>
                  <TableHead className="font-semibold text-gray-900">Status</TableHead>
                  <TableHead className="text-right font-semibold text-gray-900">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentEmployees.map((employee) => (
                  <TableRow key={employee.id} className="hover:bg-gray-50">
                    <TableCell className="py-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback className="bg-primary/10 text-primary font-medium">
                            {employee.name
                              .split(' ')
                              .map((n) => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-gray-900">{employee.name}</div>
                          <div className="text-sm text-gray-600">{employee.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <span className="text-gray-900">{employee.phone}</span>
                    </TableCell>
                    <TableCell className="py-4">
                      <span className="text-gray-900">{employee.joinDate}</span>
                    </TableCell>
                    <TableCell className="py-4">{getStatusBadge(employee.status)}</TableCell>
                    <TableCell className="py-4 text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Employee
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Remove Employee
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-700">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    return (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    );
                  })
                  .map((page, index, array) => (
                    <div key={page} className="flex items-center">
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="text-muted-foreground px-2 text-sm">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                      >
                        {page}
                      </Button>
                    </div>
                  ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {filteredEmployees.length === 0 && (
            <div className="py-12 text-center">
              <div className="flex flex-col items-center gap-2">
                <Users className="h-12 w-12 text-gray-400" />
                <p className="text-lg font-medium text-gray-900">
                  {employer.status === 'pending' ? 'No employees yet' : 'No employees found'}
                </p>
                <p className="text-sm text-gray-600">
                  {employer.status === 'pending'
                    ? 'Employees can be added after the employer is approved.'
                    : 'Try adjusting your search criteria or add a new employee.'}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card> */}
      {/* Approval Confirmation Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Approve Employer
            </DialogTitle>
            <DialogDescription asChild>
              <div>
                <p className="text-muted-foreground mb-4 text-sm">
                  Are you sure you want to approve <strong>{employer.companyName}</strong>?
                </p>
                <p className="text-muted-foreground mb-2 text-sm">This action will:</p>
                <ul className="text-muted-foreground mb-4 list-inside list-disc space-y-1 text-sm">
                  <li>Activate the employer account</li>
                  <li>Allow the employer to access all features</li>
                  <li>Enable employee management capabilities</li>
                  <li>Send approval notification to the employer</li>
                </ul>
                <p className="text-muted-foreground text-sm">This action cannot be undone.</p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmApproval} className="bg-green-600 hover:bg-green-700">
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve Employer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Employer Modal */}
      {rawEmployerData && (
        <CreateUpdateEmployerModal
          isOpen={editModalOpen}
          onClose={handleCloseEditModal}
          mode="edit"
          employerData={rawEmployerData}
          onEmployerCreated={handleEmployerUpdated}
        />
      )}
    </div>
  );
};
