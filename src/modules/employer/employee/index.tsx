'use client';

import { useState } from 'react';
import { Plus, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CreateUpdateEmployeeModal, EmployeeListTable } from './components';
import { Employee } from './api/useEmployees';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/reusable-component/header';

export const EmployeeModule = () => {
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit';
    employee?: Employee;
  }>({
    isOpen: false,
    mode: 'create',
    employee: undefined,
  });

  const handleCreateEmployee = () => {
    setModalState({
      isOpen: true,
      mode: 'create',
      employee: undefined,
    });
  };

  const handleEditEmployee = (employee: Employee) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      employee,
    });
  };

  const handleCloseModal = () => {
    setModalState({
      isOpen: false,
      mode: 'create',
      employee: undefined,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header title="Employee Management" description="Manage all employees in the system" />

        <Button onClick={handleCreateEmployee} className="bg-primary hover:bg-primary/90 h-10 px-6">
          <Plus className="mr-2 h-4 w-4" />
          Add Employee
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <User className="text-primary h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-2xl font-bold text-gray-900">10</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <User className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Employees</p>
                <p className="text-2xl font-bold text-gray-900">5</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                <User className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">KYC Pending</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Employee List */}
      <EmployeeListTable onEditEmployee={handleEditEmployee} />

      <CreateUpdateEmployeeModal
        isOpen={modalState.isOpen}
        onClose={handleCloseModal}
        mode={modalState.mode}
        employeeData={modalState.employee}
      />
    </div>
  );
};
